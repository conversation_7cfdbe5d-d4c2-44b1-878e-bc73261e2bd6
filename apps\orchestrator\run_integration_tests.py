#!/usr/bin/env python3
"""
Test runner for Dify integration tests.

This script provides an easy way to run the Dify agent integration tests
with proper environment setup and configuration.
"""

import os
import sys
import subprocess
from pathlib import Path


def load_environment():
    """Load environment variables from .env.local file."""
    env_file = Path(__file__).parent / ".env.local"
    
    if env_file.exists():
        print(f"Loading environment from {env_file}")
        with open(env_file, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    os.environ[key] = value
                    print(f"Set {key}={value}")
    else:
        print(f"Warning: Environment file not found: {env_file}")


def check_prerequisites():
    """Check if all prerequisites are met."""
    api_key = os.getenv('DIFY_API_KEY')
    if not api_key:
        print("ERROR: DIFY_API_KEY not found in environment variables")
        print("Please ensure .env.local file exists with DIFY_API_KEY set")
        return False
    
    print(f"✅ API Key found: {api_key[:10]}..." if len(api_key) > 10 else api_key)
    print(f"✅ Base URL: {os.getenv('DIFY_BASE_URL', 'https://api.dify.ai/v1')}")
    return True


def run_tests(test_type="all"):
    """Run the integration tests."""
    cmd = ["python", "-m", "pytest"]
    
    if test_type == "unit":
        cmd.extend(["-m", "unit", "-v"])
        print("Running unit tests only...")
    elif test_type == "integration":
        cmd.extend(["-m", "integration", "-v", "-s"])
        print("Running integration tests only...")
    elif test_type == "dify":
        cmd.extend(["tests/integration/test_dify_agent_invoke.py", "-v", "-s"])
        print("Running Dify agent integration tests only...")
    else:
        cmd.extend(["-v"])
        print("Running all tests...")
    
    # Add coverage if requested
    if "--coverage" in sys.argv:
        cmd.extend(["--cov=src", "--cov-report=html", "--cov-report=term"])
    
    print(f"Command: {' '.join(cmd)}")
    print("-" * 50)
    
    try:
        result = subprocess.run(cmd, cwd=Path(__file__).parent)
        return result.returncode == 0
    except KeyboardInterrupt:
        print("\nTests interrupted by user.")
        return False
    except Exception as e:
        print(f"Error running tests: {e}")
        return False


def main():
    """Main function."""
    print("Dify Integration Test Runner")
    print("=" * 50)
    
    # Load environment
    load_environment()
    print()
    
    # Check prerequisites
    if not check_prerequisites():
        return 1
    print()
    
    # Determine test type
    test_type = "all"
    if "--unit" in sys.argv:
        test_type = "unit"
    elif "--integration" in sys.argv:
        test_type = "integration"
    elif "--dify" in sys.argv:
        test_type = "dify"
    
    # Run tests
    success = run_tests(test_type)
    
    if success:
        print("\n✅ Tests completed successfully!")
        return 0
    else:
        print("\n❌ Some tests failed!")
        return 1


if __name__ == "__main__":
    if "--help" in sys.argv or "-h" in sys.argv:
        print("Usage: python run_integration_tests.py [options]")
        print()
        print("Options:")
        print("  --unit         Run unit tests only")
        print("  --integration  Run integration tests only")
        print("  --dify         Run Dify agent tests only")
        print("  --coverage     Include coverage reporting")
        print("  --help, -h     Show this help message")
        print()
        print("Examples:")
        print("  python run_integration_tests.py --dify")
        print("  python run_integration_tests.py --integration --coverage")
        sys.exit(0)
    
    exit_code = main()
    sys.exit(exit_code)
