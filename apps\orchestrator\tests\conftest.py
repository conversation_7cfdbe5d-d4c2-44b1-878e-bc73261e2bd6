"""
Pytest configuration for orchestrator tests.

This file sets up common configuration for all tests in the orchestrator project.
"""

import os
import sys
from pathlib import Path

# Set required environment variables before importing any modules
# This prevents DIFY_API_KEY errors during test collection and execution
os.environ.setdefault("DIFY_API_KEY", "test-api-key-for-unit-tests")
os.environ.setdefault("DIFY_BASE_URL", "https://api.dify.ai/v1")
os.environ.setdefault("LOG_LEVEL", "INFO")
os.environ.setdefault("PYTHON_ENV", "test")

# Add the current directory to Python path for testing
# This allows importing the local dify library during tests
current_dir = Path(__file__).parent.parent

if str(current_dir) not in sys.path:
    sys.path.insert(0, str(current_dir))

# Also add the src directory to the path
src_path = current_dir / "src"
if str(src_path) not in sys.path:
    sys.path.insert(0, str(src_path))

# Import fixtures to make them available to all tests
try:
    from .fixtures import (
        sample_tenant_config,
        sample_agent_request,
        sample_agent_request_with_conversation,
        sample_api_gateway_event,
        sample_api_gateway_event_with_conversation,
        sample_lambda_context,
    )
except ImportError:
    # If fixtures can't be imported, they won't be available to tests
    pass
