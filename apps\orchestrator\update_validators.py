#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to update Pydantic V1 @validator decorators to V2 @field_validator decorators
"""
import os
import re
from pathlib import Path

def update_pydantic_validators(file_path):
    """Update a single file from V1 to V2 validators"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Step 1: Update imports
        content = re.sub(
            r'from pydantic import ([^,\n]*),?\s*validator([^,\n]*)',
            r'from pydantic import \1, field_validator\2',
            content
        )
        content = re.sub(
            r'from pydantic import validator([^,\n]*)',
            r'from pydantic import field_validator\1',
            content
        )
        
        # Step 2: Update @validator to @field_validator and add @classmethod
        # Pattern: @validator('field1', 'field2', ...)
        # Replace with: @field_validator('field1', 'field2', ...)
        #               @classmethod
        
        # Find all @validator decorators and their following function definitions
        validator_pattern = r'(@validator\([^)]+\))\s*\n(\s*)def\s+(\w+)\s*\(\s*cls\s*,\s*v'
        
        def replace_validator(match):
            decorator = match.group(1)
            indent = match.group(2)
            func_name = match.group(3)
            
            # Replace @validator with @field_validator
            new_decorator = decorator.replace('@validator', '@field_validator')
            
            # Add @classmethod decorator
            result = f"{new_decorator}\n{indent}@classmethod\n{indent}def {func_name}(cls, v"
            return result
        
        content = re.sub(validator_pattern, replace_validator, content)
        
        # Write back the updated content
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"Updated: {file_path}")
        return True
        
    except Exception as e:
        print(f"Error updating {file_path}: {e}")
        return False

def main():
    """Main function to update all Dify model files"""
    # Define the base directory
    base_dir = Path(__file__).parent / "dify"
    
    # Find all Python files in dify directory and subdirectories
    python_files = []
    for root, dirs, files in os.walk(base_dir):
        for file in files:
            if file.endswith('.py'):
                python_files.append(os.path.join(root, file))
    
    print(f"Found {len(python_files)} Python files to process...")
    
    # Update each file
    updated_count = 0
    for file_path in python_files:
        if update_pydantic_validators(file_path):
            updated_count += 1
    
    print(f"\nUpdated {updated_count}/{len(python_files)} files successfully.")

if __name__ == "__main__":
    main()
